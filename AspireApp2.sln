Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Api", "Api", "{C2BE6BA6-CC47-15FF-2C16-B63540480708}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{9FF7CF49-05F8-C390-1437-CEAFABD6FA4A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Web", "Web", "{DBDA4676-CCFC-D922-B594-1F13ABB9AA0E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{266364A7-970B-0018-365E-01D830874B73}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{B1C2D3E4-F5A6-7B8C-9D0E-F1A2B3C4D5E6}"
	ProjectSection(SolutionItems) = preProject
		tests\README.md = tests\README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{C2D3E4F5-A6B7-8C9D-0E1F-A2B3C4D5E6F7}"
	ProjectSection(SolutionItems) = preProject
		docs\README.md = docs\README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "build", "build", "{D3E4F5A6-B7C8-9D0E-1F2A-B3C4D5E6F7A8}"
	ProjectSection(SolutionItems) = preProject
		build\README.md = build\README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{089100B1-113F-4E66-888A-E83F3999EAFD}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		.gitignore = .gitignore
		Directory.Build.props = Directory.Build.props
		global.json = global.json
		README.md = README.md
		test-api.sh = test-api.sh
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AspireApp2.AppHost", "AspireApp2.AppHost\AspireApp2.AppHost.csproj", "{A9B36EC0-8DEB-4E06-B2FA-936CD8A52B7C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AspireApp2.ServiceDefaults", "AspireApp2.ServiceDefaults\AspireApp2.ServiceDefaults.csproj", "{35A6DD69-8C50-4BBC-9C8B-4DBA2DD09D33}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AspireApp2.Web", "AspireApp2.Web\AspireApp2.Web.csproj", "{93C6EC6A-DE69-46C7-B1C0-D7439C70051C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AspireApp2.PlayerEventService", "AspireApp2.PlayerEventService\AspireApp2.PlayerEventService.csproj", "{B2F4A9E5-3C8D-4E0F-9F7A-8A1B2C3D4E5F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AspireApp2.Api.Domain", "src\Api\AspireApp2.Api.Domain\AspireApp2.Api.Domain.csproj", "{819E59AE-7C54-43F1-A96E-8885B79AC92B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AspireApp2.Api.Application", "src\Api\AspireApp2.Api.Application\AspireApp2.Api.Application.csproj", "{9A23BEF9-56C6-454C-AFB0-EC2478F98BF5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AspireApp2.Api.Infrastructure", "src\Api\AspireApp2.Api.Infrastructure\AspireApp2.Api.Infrastructure.csproj", "{F1396E5C-1E3B-48D7-96CC-F0B6A0B2304E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AspireApp2.Api.Presentation", "src\Api\AspireApp2.Api.Presentation\AspireApp2.Api.Presentation.csproj", "{22B05CD0-05AC-40A8-BC19-4CF549D7120F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A9B36EC0-8DEB-4E06-B2FA-936CD8A52B7C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A9B36EC0-8DEB-4E06-B2FA-936CD8A52B7C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A9B36EC0-8DEB-4E06-B2FA-936CD8A52B7C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A9B36EC0-8DEB-4E06-B2FA-936CD8A52B7C}.Debug|x64.Build.0 = Debug|Any CPU
		{A9B36EC0-8DEB-4E06-B2FA-936CD8A52B7C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A9B36EC0-8DEB-4E06-B2FA-936CD8A52B7C}.Debug|x86.Build.0 = Debug|Any CPU
		{A9B36EC0-8DEB-4E06-B2FA-936CD8A52B7C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A9B36EC0-8DEB-4E06-B2FA-936CD8A52B7C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A9B36EC0-8DEB-4E06-B2FA-936CD8A52B7C}.Release|x64.ActiveCfg = Release|Any CPU
		{A9B36EC0-8DEB-4E06-B2FA-936CD8A52B7C}.Release|x64.Build.0 = Release|Any CPU
		{A9B36EC0-8DEB-4E06-B2FA-936CD8A52B7C}.Release|x86.ActiveCfg = Release|Any CPU
		{A9B36EC0-8DEB-4E06-B2FA-936CD8A52B7C}.Release|x86.Build.0 = Release|Any CPU
		{35A6DD69-8C50-4BBC-9C8B-4DBA2DD09D33}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{35A6DD69-8C50-4BBC-9C8B-4DBA2DD09D33}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{35A6DD69-8C50-4BBC-9C8B-4DBA2DD09D33}.Debug|x64.ActiveCfg = Debug|Any CPU
		{35A6DD69-8C50-4BBC-9C8B-4DBA2DD09D33}.Debug|x64.Build.0 = Debug|Any CPU
		{35A6DD69-8C50-4BBC-9C8B-4DBA2DD09D33}.Debug|x86.ActiveCfg = Debug|Any CPU
		{35A6DD69-8C50-4BBC-9C8B-4DBA2DD09D33}.Debug|x86.Build.0 = Debug|Any CPU
		{35A6DD69-8C50-4BBC-9C8B-4DBA2DD09D33}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{35A6DD69-8C50-4BBC-9C8B-4DBA2DD09D33}.Release|Any CPU.Build.0 = Release|Any CPU
		{35A6DD69-8C50-4BBC-9C8B-4DBA2DD09D33}.Release|x64.ActiveCfg = Release|Any CPU
		{35A6DD69-8C50-4BBC-9C8B-4DBA2DD09D33}.Release|x64.Build.0 = Release|Any CPU
		{35A6DD69-8C50-4BBC-9C8B-4DBA2DD09D33}.Release|x86.ActiveCfg = Release|Any CPU
		{35A6DD69-8C50-4BBC-9C8B-4DBA2DD09D33}.Release|x86.Build.0 = Release|Any CPU
		{93C6EC6A-DE69-46C7-B1C0-D7439C70051C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{93C6EC6A-DE69-46C7-B1C0-D7439C70051C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{93C6EC6A-DE69-46C7-B1C0-D7439C70051C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{93C6EC6A-DE69-46C7-B1C0-D7439C70051C}.Debug|x64.Build.0 = Debug|Any CPU
		{93C6EC6A-DE69-46C7-B1C0-D7439C70051C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{93C6EC6A-DE69-46C7-B1C0-D7439C70051C}.Debug|x86.Build.0 = Debug|Any CPU
		{93C6EC6A-DE69-46C7-B1C0-D7439C70051C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{93C6EC6A-DE69-46C7-B1C0-D7439C70051C}.Release|Any CPU.Build.0 = Release|Any CPU
		{93C6EC6A-DE69-46C7-B1C0-D7439C70051C}.Release|x64.ActiveCfg = Release|Any CPU
		{93C6EC6A-DE69-46C7-B1C0-D7439C70051C}.Release|x64.Build.0 = Release|Any CPU
		{93C6EC6A-DE69-46C7-B1C0-D7439C70051C}.Release|x86.ActiveCfg = Release|Any CPU
		{93C6EC6A-DE69-46C7-B1C0-D7439C70051C}.Release|x86.Build.0 = Release|Any CPU
		{B2F4A9E5-3C8D-4E0F-9F7A-8A1B2C3D4E5F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2F4A9E5-3C8D-4E0F-9F7A-8A1B2C3D4E5F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2F4A9E5-3C8D-4E0F-9F7A-8A1B2C3D4E5F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B2F4A9E5-3C8D-4E0F-9F7A-8A1B2C3D4E5F}.Debug|x64.Build.0 = Debug|Any CPU
		{B2F4A9E5-3C8D-4E0F-9F7A-8A1B2C3D4E5F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B2F4A9E5-3C8D-4E0F-9F7A-8A1B2C3D4E5F}.Debug|x86.Build.0 = Debug|Any CPU
		{B2F4A9E5-3C8D-4E0F-9F7A-8A1B2C3D4E5F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2F4A9E5-3C8D-4E0F-9F7A-8A1B2C3D4E5F}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2F4A9E5-3C8D-4E0F-9F7A-8A1B2C3D4E5F}.Release|x64.ActiveCfg = Release|Any CPU
		{B2F4A9E5-3C8D-4E0F-9F7A-8A1B2C3D4E5F}.Release|x64.Build.0 = Release|Any CPU
		{B2F4A9E5-3C8D-4E0F-9F7A-8A1B2C3D4E5F}.Release|x86.ActiveCfg = Release|Any CPU
		{B2F4A9E5-3C8D-4E0F-9F7A-8A1B2C3D4E5F}.Release|x86.Build.0 = Release|Any CPU
		{819E59AE-7C54-43F1-A96E-8885B79AC92B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{819E59AE-7C54-43F1-A96E-8885B79AC92B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{819E59AE-7C54-43F1-A96E-8885B79AC92B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{819E59AE-7C54-43F1-A96E-8885B79AC92B}.Debug|x64.Build.0 = Debug|Any CPU
		{819E59AE-7C54-43F1-A96E-8885B79AC92B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{819E59AE-7C54-43F1-A96E-8885B79AC92B}.Debug|x86.Build.0 = Debug|Any CPU
		{819E59AE-7C54-43F1-A96E-8885B79AC92B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{819E59AE-7C54-43F1-A96E-8885B79AC92B}.Release|Any CPU.Build.0 = Release|Any CPU
		{819E59AE-7C54-43F1-A96E-8885B79AC92B}.Release|x64.ActiveCfg = Release|Any CPU
		{819E59AE-7C54-43F1-A96E-8885B79AC92B}.Release|x64.Build.0 = Release|Any CPU
		{819E59AE-7C54-43F1-A96E-8885B79AC92B}.Release|x86.ActiveCfg = Release|Any CPU
		{819E59AE-7C54-43F1-A96E-8885B79AC92B}.Release|x86.Build.0 = Release|Any CPU
		{9A23BEF9-56C6-454C-AFB0-EC2478F98BF5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A23BEF9-56C6-454C-AFB0-EC2478F98BF5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A23BEF9-56C6-454C-AFB0-EC2478F98BF5}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9A23BEF9-56C6-454C-AFB0-EC2478F98BF5}.Debug|x64.Build.0 = Debug|Any CPU
		{9A23BEF9-56C6-454C-AFB0-EC2478F98BF5}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9A23BEF9-56C6-454C-AFB0-EC2478F98BF5}.Debug|x86.Build.0 = Debug|Any CPU
		{9A23BEF9-56C6-454C-AFB0-EC2478F98BF5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A23BEF9-56C6-454C-AFB0-EC2478F98BF5}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A23BEF9-56C6-454C-AFB0-EC2478F98BF5}.Release|x64.ActiveCfg = Release|Any CPU
		{9A23BEF9-56C6-454C-AFB0-EC2478F98BF5}.Release|x64.Build.0 = Release|Any CPU
		{9A23BEF9-56C6-454C-AFB0-EC2478F98BF5}.Release|x86.ActiveCfg = Release|Any CPU
		{9A23BEF9-56C6-454C-AFB0-EC2478F98BF5}.Release|x86.Build.0 = Release|Any CPU
		{F1396E5C-1E3B-48D7-96CC-F0B6A0B2304E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F1396E5C-1E3B-48D7-96CC-F0B6A0B2304E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F1396E5C-1E3B-48D7-96CC-F0B6A0B2304E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F1396E5C-1E3B-48D7-96CC-F0B6A0B2304E}.Debug|x64.Build.0 = Debug|Any CPU
		{F1396E5C-1E3B-48D7-96CC-F0B6A0B2304E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F1396E5C-1E3B-48D7-96CC-F0B6A0B2304E}.Debug|x86.Build.0 = Debug|Any CPU
		{F1396E5C-1E3B-48D7-96CC-F0B6A0B2304E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F1396E5C-1E3B-48D7-96CC-F0B6A0B2304E}.Release|Any CPU.Build.0 = Release|Any CPU
		{F1396E5C-1E3B-48D7-96CC-F0B6A0B2304E}.Release|x64.ActiveCfg = Release|Any CPU
		{F1396E5C-1E3B-48D7-96CC-F0B6A0B2304E}.Release|x64.Build.0 = Release|Any CPU
		{F1396E5C-1E3B-48D7-96CC-F0B6A0B2304E}.Release|x86.ActiveCfg = Release|Any CPU
		{F1396E5C-1E3B-48D7-96CC-F0B6A0B2304E}.Release|x86.Build.0 = Release|Any CPU
		{22B05CD0-05AC-40A8-BC19-4CF549D7120F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{22B05CD0-05AC-40A8-BC19-4CF549D7120F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{22B05CD0-05AC-40A8-BC19-4CF549D7120F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{22B05CD0-05AC-40A8-BC19-4CF549D7120F}.Debug|x64.Build.0 = Debug|Any CPU
		{22B05CD0-05AC-40A8-BC19-4CF549D7120F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{22B05CD0-05AC-40A8-BC19-4CF549D7120F}.Debug|x86.Build.0 = Debug|Any CPU
		{22B05CD0-05AC-40A8-BC19-4CF549D7120F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{22B05CD0-05AC-40A8-BC19-4CF549D7120F}.Release|Any CPU.Build.0 = Release|Any CPU
		{22B05CD0-05AC-40A8-BC19-4CF549D7120F}.Release|x64.ActiveCfg = Release|Any CPU
		{22B05CD0-05AC-40A8-BC19-4CF549D7120F}.Release|x64.Build.0 = Release|Any CPU
		{22B05CD0-05AC-40A8-BC19-4CF549D7120F}.Release|x86.ActiveCfg = Release|Any CPU
		{22B05CD0-05AC-40A8-BC19-4CF549D7120F}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C2BE6BA6-CC47-15FF-2C16-B63540480708} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{9FF7CF49-05F8-C390-1437-CEAFABD6FA4A} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{DBDA4676-CCFC-D922-B594-1F13ABB9AA0E} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{266364A7-970B-0018-365E-01D830874B73} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{819E59AE-7C54-43F1-A96E-8885B79AC92B} = {C2BE6BA6-CC47-15FF-2C16-B63540480708}
		{9A23BEF9-56C6-454C-AFB0-EC2478F98BF5} = {C2BE6BA6-CC47-15FF-2C16-B63540480708}
		{F1396E5C-1E3B-48D7-96CC-F0B6A0B2304E} = {C2BE6BA6-CC47-15FF-2C16-B63540480708}
		{22B05CD0-05AC-40A8-BC19-4CF549D7120F} = {C2BE6BA6-CC47-15FF-2C16-B63540480708}
		{B2F4A9E5-3C8D-4E0F-9F7A-8A1B2C3D4E5F} = {9FF7CF49-05F8-C390-1437-CEAFABD6FA4A}
		{93C6EC6A-DE69-46C7-B1C0-D7439C70051C} = {DBDA4676-CCFC-D922-B594-1F13ABB9AA0E}
		{A9B36EC0-8DEB-4E06-B2FA-936CD8A52B7C} = {266364A7-970B-0018-365E-01D830874B73}
		{35A6DD69-8C50-4BBC-9C8B-4DBA2DD09D33} = {266364A7-970B-0018-365E-01D830874B73}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-5678-9ABC-123456789012}
	EndGlobalSection
EndGlobal
