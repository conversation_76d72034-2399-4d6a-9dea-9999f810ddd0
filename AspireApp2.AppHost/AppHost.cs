using Projects;

IDistributedApplicationBuilder builder = DistributedApplication.CreateBuilder(args);

// Add infrastructure services
IResourceBuilder<RedisResource> cache = builder.AddRedis("cache");

IResourceBuilder<SqlServerDatabaseResource> sqlServer = builder.AddSqlServer("sql")
    .WithDataVolume()
    .AddDatabase("DefaultConnection");

IResourceBuilder<MongoDBDatabaseResource> mongodb = builder.AddMongoDB("mongodb")
    .WithDataVolume()
    .AddDatabase("PlayerEvents");

// Add Kafka using the official Aspire integration
IResourceBuilder<KafkaServerResource> kafka = builder.AddKafka("kafka")
    .WithKafkaUI(); // Optional: adds Kafka UI for monitoring

// Updated to use the new Clean Architecture API
IResourceBuilder<ProjectResource> apiService = builder.AddProject<AspireApp2_Api_Presentation>("apiservice")
    .WithReference(sqlServer)
    .WithReference(kafka)
    .WithHttpHealthCheck("/health")
    .WaitFor(sqlServer)
    .WaitFor(kafka);

IResourceBuilder<ProjectResource> playerEventService = builder
    .AddProject<AspireApp2_PlayerEventService>("playereventservice")
    .WithReference(mongodb)
    .WithReference(kafka)
    .WithHttpHealthCheck("/health")
    .WaitFor(mongodb)
    .WaitFor(kafka);

builder.AddProject<AspireApp2_Web>("webfrontend")
    .WithExternalHttpEndpoints()
    .WithHttpHealthCheck("/health")
    .WithReference(cache)
    .WaitFor(cache)
    .WithReference(apiService)
    .WaitFor(apiService);

builder.Build().Run();
