<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.3.1"/>

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UserSecretsId>decb4897-93ea-4ba9-b058-05ba08763aa9</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\src\Api\AspireApp2.Api.Presentation\AspireApp2.Api.Presentation.csproj"/>
    <ProjectReference Include="..\AspireApp2.Web\AspireApp2.Web.csproj"/>
    <ProjectReference Include="..\AspireApp2.PlayerEventService\AspireApp2.PlayerEventService.csproj"/>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="9.3.1"/>
    <PackageReference Include="Aspire.Hosting.Kafka" Version="9.3.1"/>
    <PackageReference Include="Aspire.Hosting.MongoDB" Version="9.3.1"/>
    <PackageReference Include="Aspire.Hosting.Redis" Version="9.3.1"/>
    <PackageReference Include="Aspire.Hosting.SqlServer" Version="9.3.1"/>
    <PackageReference Include="Polly" Version="8.6.1"/>
    <PackageReference Include="Polly.Extensions" Version="8.6.1"/>
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0"/>
    <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2"/>
  </ItemGroup>

</Project>
