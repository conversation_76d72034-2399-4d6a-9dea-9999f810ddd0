using AspireApp2.Api.Domain.Entities;

namespace AspireApp2.Api.Domain.Repositories;

/// <summary>
/// Repository interface for Item entity
/// </summary>
public interface IItemRepository
{
    Task<IEnumerable<Item>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<Item?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<Item> CreateAsync(Item item, CancellationToken cancellationToken = default);
    Task<Item> UpdateAsync(Item item, CancellationToken cancellationToken = default);
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(int id, CancellationToken cancellationToken = default);
}
