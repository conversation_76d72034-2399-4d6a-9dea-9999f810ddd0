using AspireApp2.Api.Domain.Entities;

namespace AspireApp2.Api.Domain.Repositories;

/// <summary>
/// Repository interface for OutboxMessage entity
/// </summary>
public interface IOutboxRepository
{
    Task<OutboxMessage> CreateAsync(OutboxMessage message, CancellationToken cancellationToken = default);
    Task<IEnumerable<OutboxMessage>> GetUnprocessedMessagesAsync(int batchSize = 50, CancellationToken cancellationToken = default);
    Task<OutboxMessage> UpdateAsync(OutboxMessage message, CancellationToken cancellationToken = default);
    Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<OutboxMessage>> GetFailedMessagesAsync(int batchSize = 50, CancellationToken cancellationToken = default);
    Task<IEnumerable<OutboxMessage>> GetExpiredMessagesAsync(TimeSpan maxAge, int batchSize = 50, CancellationToken cancellationToken = default);
}
