using AspireApp2.Api.Domain.Entities;

namespace AspireApp2.Api.Domain.Repositories;

/// <summary>
/// Repository interface for Player entity
/// </summary>
public interface IPlayerRepository
{
    Task<IEnumerable<Player>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<Player?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<Player?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<IEnumerable<Player>> GetByTeamAsync(string team, CancellationToken cancellationToken = default);
    Task<Player> CreateAsync(Player player, CancellationToken cancellationToken = default);
    Task<Player> UpdateAsync(Player player, CancellationToken cancellationToken = default);
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(int id, CancellationToken cancellationToken = default);
    Task<bool> IsEmailUniqueAsync(string email, int? excludeId = null, CancellationToken cancellationToken = default);
    Task<bool> IsJerseyNumberUniqueAsync(int jerseyNumber, string team, int? excludeId = null, CancellationToken cancellationToken = default);
}
