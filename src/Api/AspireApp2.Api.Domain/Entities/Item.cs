namespace AspireApp2.Api.Domain.Entities;

/// <summary>
/// Represents an item entity in the domain
/// </summary>
public class Item
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Domain methods
    public void UpdateDetails(string name, string description)
    {
        Name = name?.Trim() ?? throw new ArgumentNullException(nameof(name));
        Description = description?.Trim() ?? string.Empty;
    }

    // Domain validation
    public bool IsValidName()
    {
        return !string.IsNullOrWhiteSpace(Name) && Name.Length <= 255;
    }

    public bool IsValidDescription()
    {
        return Description.Length <= 1000;
    }
}
