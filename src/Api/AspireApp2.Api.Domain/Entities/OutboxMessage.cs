namespace AspireApp2.Api.Domain.Entities;

/// <summary>
/// Represents an outbox message for implementing the outbox pattern
/// </summary>
public class OutboxMessage
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Topic { get; set; } = string.Empty;
    public string Key { get; set; } = string.Empty;
    public string Payload { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? ProcessedAt { get; set; }
    public int RetryCount { get; set; } = 0;
    public DateTime? LastRetryAt { get; set; }
    public string? ErrorMessage { get; set; }
    public bool IsFailed { get; set; } = false;

    // Domain methods
    public void MarkAsProcessed()
    {
        ProcessedAt = DateTime.UtcNow;
        IsFailed = false;
        ErrorMessage = null;
    }

    public void MarkAsFailed(string errorMessage)
    {
        IsFailed = true;
        ErrorMessage = errorMessage?.Trim();
        LastRetryAt = DateTime.UtcNow;
        RetryCount++;
    }

    public void ResetForRetry()
    {
        IsFailed = false;
        ErrorMessage = null;
        LastRetryAt = DateTime.UtcNow;
        RetryCount++;
    }

    // Domain validation
    public bool CanRetry(int maxRetries = 3)
    {
        return RetryCount < maxRetries;
    }

    public bool IsExpired(TimeSpan maxAge)
    {
        return DateTime.UtcNow - CreatedAt > maxAge;
    }
}
