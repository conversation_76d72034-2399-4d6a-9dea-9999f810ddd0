using AspireApp2.Api.Domain.Entities;

namespace AspireApp2.Api.Domain.Events;

/// <summary>
/// Domain event raised when a player is created
/// </summary>
public class PlayerCreatedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Player Player { get; }

    public PlayerCreatedEvent(Player player)
    {
        Player = player;
    }
}
