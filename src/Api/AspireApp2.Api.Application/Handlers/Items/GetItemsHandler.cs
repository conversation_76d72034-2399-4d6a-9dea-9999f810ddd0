using AspireApp2.Api.Application.Queries.Items;
using AspireApp2.Api.Domain.Entities;
using AspireApp2.Api.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AspireApp2.Api.Application.Handlers.Items;

/// <summary>
/// Handler for getting items
/// </summary>
public class GetItemsHandler : IRequestHandler<GetItemsQuery, IEnumerable<Item>>
{
    private readonly ILogger<GetItemsHandler> _logger;
    private readonly IItemRepository _itemRepository;

    public GetItemsHandler(IItemRepository itemRepository, ILogger<GetItemsHandler> logger)
    {
        _itemRepository = itemRepository;
        _logger = logger;
    }

    public async Task<IEnumerable<Item>> Handle(GetItemsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Retrieving all items");

        try
        {
            return await _itemRepository.GetAllAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving items");
            throw;
        }
    }
}

/// <summary>
/// Handler for getting an item by ID
/// </summary>
public class GetItemByIdHandler : IRequestHandler<GetItemByIdQuery, Item?>
{
    private readonly ILogger<GetItemByIdHandler> _logger;
    private readonly IItemRepository _itemRepository;

    public GetItemByIdHandler(IItemRepository itemRepository, ILogger<GetItemByIdHandler> logger)
    {
        _itemRepository = itemRepository;
        _logger = logger;
    }

    public async Task<Item?> Handle(GetItemByIdQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Retrieving item with ID: {ItemId}", request.Id);

        try
        {
            return await _itemRepository.GetByIdAsync(request.Id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving item with ID: {ItemId}", request.Id);
            throw;
        }
    }
}
