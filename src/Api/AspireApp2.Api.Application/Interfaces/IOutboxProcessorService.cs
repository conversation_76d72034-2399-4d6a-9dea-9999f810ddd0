namespace AspireApp2.Api.Application.Interfaces;

/// <summary>
/// Service interface for processing outbox messages
/// </summary>
public interface IOutboxProcessorService
{
    /// <summary>
    /// Processes pending outbox messages
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task ProcessPendingMessagesAsync(CancellationToken cancellationToken = default);
}
