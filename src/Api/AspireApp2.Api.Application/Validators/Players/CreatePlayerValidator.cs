using AspireApp2.Api.Application.Commands.Players;
using FluentValidation;

namespace AspireApp2.Api.Application.Validators.Players;

/// <summary>
/// Validator for CreatePlayerCommand
/// </summary>
public class CreatePlayerValidator : AbstractValidator<CreatePlayerCommand>
{
    public CreatePlayerValidator()
    {
        RuleFor(x => x.FirstName)
            .NotEmpty().WithMessage("First name is required")
            .Length(2, 50).WithMessage("First name must be between 2 and 50 characters")
            .Matches("^[a-zA-Z\\s'-]+$")
            .WithMessage("First name can only contain letters, spaces, hyphens, and apostrophes");

        RuleFor(x => x.LastName)
            .NotEmpty().WithMessage("Last name is required")
            .Length(2, 50).WithMessage("Last name must be between 2 and 50 characters")
            .Matches("^[a-zA-Z\\s'-]+$")
            .WithMessage("Last name can only contain letters, spaces, hyphens, and apostrophes");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Email must be a valid email address")
            .MaximumLength(100).WithMessage("Email cannot exceed 100 characters");

        RuleFor(x => x.Position)
            .NotEmpty().WithMessage("Position is required")
            .Length(2, 30).WithMessage("Position must be between 2 and 30 characters")
            .Must(BeValidPosition).WithMessage("Position must be one of: Goalkeeper, Defender, Midfielder, Forward");

        RuleFor(x => x.JerseyNumber)
            .InclusiveBetween(1, 99).WithMessage("Jersey number must be between 1 and 99");

        RuleFor(x => x.DateOfBirth)
            .NotEmpty().WithMessage("Date of birth is required")
            .LessThan(DateTime.Today.AddYears(-16)).WithMessage("Player must be at least 16 years old")
            .GreaterThan(DateTime.Today.AddYears(-50)).WithMessage("Player cannot be older than 50 years");

        RuleFor(x => x.Team)
            .NotEmpty().WithMessage("Team is required")
            .Length(2, 50).WithMessage("Team name must be between 2 and 50 characters")
            .Matches("^[a-zA-Z\\s0-9'-]+$")
            .WithMessage("Team name can only contain letters, numbers, spaces, hyphens, and apostrophes");

        RuleFor(x => x.Salary)
            .GreaterThanOrEqualTo(0).WithMessage("Salary cannot be negative")
            .LessThanOrEqualTo(1000000000).WithMessage("Salary cannot exceed 1 billion");
    }

    private static bool BeValidPosition(string position)
    {
        string[] validPositions = new[] { "Goalkeeper", "Defender", "Midfielder", "Forward" };
        return validPositions.Contains(position, StringComparer.OrdinalIgnoreCase);
    }
}
