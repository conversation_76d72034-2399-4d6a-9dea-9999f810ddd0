using AspireApp2.Api.Domain.Entities;
using AspireApp2.Api.Domain.Repositories;
using AspireApp2.Api.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AspireApp2.Api.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for OutboxMessage entity
/// </summary>
public class OutboxRepository : IOutboxRepository
{
    private readonly ApiDbContext _context;
    private readonly ILogger<OutboxRepository> _logger;

    public OutboxRepository(ApiDbContext context, ILogger<OutboxRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<OutboxMessage> CreateAsync(OutboxMessage message, CancellationToken cancellationToken = default)
    {
        try
        {
            _context.OutboxMessages.Add(message);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Created outbox message {MessageId} for topic {Topic}", message.Id, message.Topic);
            return message;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating outbox message for topic {Topic}", message.Topic);
            throw;
        }
    }

    public async Task<IEnumerable<OutboxMessage>> GetUnprocessedMessagesAsync(int batchSize = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.OutboxMessages
                .Where(m => m.ProcessedAt == null && !m.IsFailed)
                .OrderBy(m => m.CreatedAt)
                .Take(batchSize)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving unprocessed outbox messages");
            throw;
        }
    }

    public async Task<OutboxMessage> UpdateAsync(OutboxMessage message, CancellationToken cancellationToken = default)
    {
        try
        {
            _context.Entry(message).State = EntityState.Modified;
            await _context.SaveChangesAsync(cancellationToken);

            return message;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating outbox message {MessageId}", message.Id);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            OutboxMessage? message = await _context.OutboxMessages
                .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);

            if (message == null)
            {
                return false;
            }

            _context.OutboxMessages.Remove(message);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Deleted outbox message {MessageId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting outbox message {MessageId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<OutboxMessage>> GetFailedMessagesAsync(int batchSize = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.OutboxMessages
                .Where(m => m.IsFailed)
                .OrderBy(m => m.LastRetryAt)
                .Take(batchSize)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving failed outbox messages");
            throw;
        }
    }

    public async Task<IEnumerable<OutboxMessage>> GetExpiredMessagesAsync(TimeSpan maxAge, int batchSize = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            DateTime cutoffTime = DateTime.UtcNow - maxAge;

            return await _context.OutboxMessages
                .Where(m => m.CreatedAt < cutoffTime)
                .OrderBy(m => m.CreatedAt)
                .Take(batchSize)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving expired outbox messages");
            throw;
        }
    }
}
