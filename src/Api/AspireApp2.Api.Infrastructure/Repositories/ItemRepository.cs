using AspireApp2.Api.Domain.Entities;
using AspireApp2.Api.Domain.Repositories;
using AspireApp2.Api.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AspireApp2.Api.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for Item entity
/// </summary>
public class ItemRepository : IItemRepository
{
    private readonly ApiDbContext _context;
    private readonly ILogger<ItemRepository> _logger;

    public ItemRepository(ApiDbContext context, ILogger<ItemRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<Item>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Items
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all items");
            throw;
        }
    }

    public async Task<Item?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Items
                .FirstOrDefaultAsync(i => i.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving item {ItemId}", id);
            throw;
        }
    }

    public async Task<Item> CreateAsync(Item item, CancellationToken cancellationToken = default)
    {
        try
        {
            _context.Items.Add(item);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created item {ItemId} with name {ItemName}", item.Id, item.Name);
            return item;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating item {ItemName}", item.Name);
            throw;
        }
    }

    public async Task<Item> UpdateAsync(Item item, CancellationToken cancellationToken = default)
    {
        try
        {
            _context.Entry(item).State = EntityState.Modified;
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated item {ItemId}", item.Id);
            return item;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating item {ItemId}", item.Id);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            Item? item = await GetByIdAsync(id, cancellationToken);
            if (item == null)
            {
                return false;
            }

            _context.Items.Remove(item);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Deleted item {ItemId}", item.Id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting item {ItemId}", id);
            throw;
        }
    }

    public async Task<bool> ExistsAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Items
                .AnyAsync(i => i.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if item {ItemId} exists", id);
            throw;
        }
    }
}
