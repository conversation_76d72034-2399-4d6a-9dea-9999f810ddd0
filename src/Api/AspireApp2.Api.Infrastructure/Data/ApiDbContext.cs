using AspireApp2.Api.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace AspireApp2.Api.Infrastructure.Data;

/// <summary>
/// Entity Framework DbContext for the API
/// </summary>
public class ApiDbContext : DbContext
{
    public ApiDbContext(DbContextOptions<ApiDbContext> options) : base(options)
    {
    }

    public DbSet<Item> Items { get; set; }
    public DbSet<Player> Players { get; set; }
    public DbSet<OutboxMessage> OutboxMessages { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<OutboxMessage>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.ProcessedAt);
            entity.HasIndex(e => new { e.IsFailed, e.ProcessedAt });
            entity.HasIndex(e => e.CreatedAt);

            entity.Property(e => e.Topic)
                .IsRequired()
                .HasMaxLength(255);

            entity.Property(e => e.Key)
                .IsRequired()
                .HasMaxLength(500);

            entity.Property(e => e.Payload)
                .IsRequired();

            entity.Property(e => e.ErrorMessage)
                .HasMaxLength(2000);
        });

        modelBuilder.Entity<Item>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.CreatedAt);

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(255);

            entity.Property(e => e.Description)
                .HasMaxLength(1000);
        });

        modelBuilder.Entity<Player>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.Email).IsUnique();
            entity.HasIndex(e => new { e.Team, e.JerseyNumber }).IsUnique();
            entity.HasIndex(e => e.Team);
            entity.HasIndex(e => e.CreatedAt);
            entity.HasIndex(e => e.IsActive);

            entity.Property(e => e.FirstName)
                .IsRequired()
                .HasMaxLength(50);

            entity.Property(e => e.LastName)
                .IsRequired()
                .HasMaxLength(50);

            entity.Property(e => e.Email)
                .IsRequired()
                .HasMaxLength(100);

            entity.Property(e => e.Position)
                .IsRequired()
                .HasMaxLength(30);

            entity.Property(e => e.Team)
                .IsRequired()
                .HasMaxLength(50);

            entity.Property(e => e.Salary)
                .HasColumnType("decimal(18,2)");
        });
    }
}
