using System.Text.Json;
using AspireApp2.Api.Application.Interfaces;
using AspireApp2.Api.Domain.Entities;
using AspireApp2.Api.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace AspireApp2.Api.Infrastructure.Services;

/// <summary>
/// Service implementation for managing outbox messages
/// </summary>
public class OutboxService : IOutboxService
{
    private readonly IOutboxRepository _outboxRepository;
    private readonly ILogger<OutboxService> _logger;

    public OutboxService(IOutboxRepository outboxRepository, ILogger<OutboxService> logger)
    {
        _outboxRepository = outboxRepository;
        _logger = logger;
    }

    public async Task AddMessageAsync(string topic, string key, object payload, CancellationToken cancellationToken = default)
    {
        try
        {
            string serializedPayload = JsonSerializer.Serialize(payload);

            var outboxMessage = new OutboxMessage
            {
                Topic = topic,
                Key = key,
                Payload = serializedPayload,
                CreatedAt = DateTime.UtcNow
            };

            await _outboxRepository.CreateAsync(outboxMessage, cancellationToken);

            _logger.LogDebug("Added outbox message {MessageId} for topic {Topic} with key {Key}",
                outboxMessage.Id, topic, key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add outbox message for topic {Topic} with key {Key}", topic, key);
            throw;
        }
    }

    public async Task<bool> ProcessUnprocessedMessagesAsync(int batchSize = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            var unprocessedMessages = await _outboxRepository.GetUnprocessedMessagesAsync(batchSize, cancellationToken);
            var messagesList = unprocessedMessages.ToList();

            if (!messagesList.Any())
            {
                return false;
            }

            _logger.LogInformation("Processing {Count} unprocessed outbox messages", messagesList.Count);

            foreach (var message in messagesList)
            {
                try
                {
                    // Here you would integrate with your Kafka producer
                    // For now, we'll just mark as processed
                    message.MarkAsProcessed();
                    await _outboxRepository.UpdateAsync(message, cancellationToken);

                    _logger.LogDebug("Processed outbox message {MessageId}", message.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process outbox message {MessageId}", message.Id);
                    message.MarkAsFailed(ex.Message);
                    await _outboxRepository.UpdateAsync(message, cancellationToken);
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing unprocessed outbox messages");
            throw;
        }
    }
}
