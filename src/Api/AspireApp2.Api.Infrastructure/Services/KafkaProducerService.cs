using AspireApp2.Api.Application.Interfaces;
using Confluent.Kafka;
using Microsoft.Extensions.Logging;

namespace AspireApp2.Api.Infrastructure.Services;

/// <summary>
/// Service implementation for Kafka message production
/// </summary>
public class KafkaProducerService : IKafkaProducerService
{
    private readonly IProducer<string, string> _producer;
    private readonly ILogger<KafkaProducerService> _logger;

    public KafkaProducerService(IProducer<string, string> producer, ILogger<KafkaProducerService> logger)
    {
        _producer = producer;
        _logger = logger;
    }

    public async Task PublishAsync(string topic, string key, string message, CancellationToken cancellationToken = default)
    {
        try
        {
            var kafkaMessage = new Message<string, string>
            {
                Key = key,
                Value = message,
                Headers = new Headers
                {
                    { "timestamp", System.Text.Encoding.UTF8.GetBytes(DateTime.UtcNow.ToString("O")) },
                    { "source", System.Text.Encoding.UTF8.GetBytes("AspireApp2.Api") }
                }
            };

            var deliveryResult = await _producer.ProduceAsync(topic, kafkaMessage, cancellationToken);

            _logger.LogDebug("Successfully published message to topic {Topic} with key {Key}. Partition: {Partition}, Offset: {Offset}",
                topic, key, deliveryResult.Partition.Value, deliveryResult.Offset.Value);
        }
        catch (ProduceException<string, string> ex)
        {
            _logger.LogError(ex, "Failed to publish message to topic {Topic} with key {Key}. Error: {ErrorCode} - {ErrorReason}",
                topic, key, ex.Error.Code, ex.Error.Reason);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error publishing message to topic {Topic} with key {Key}", topic, key);
            throw;
        }
    }
}
