using System.Net;
using AspireApp2.Api.Application.Commands.Players;
using AspireApp2.Api.Application.DTOs;
using AspireApp2.Api.Application.Queries.Players;
using AspireApp2.Api.Domain.Entities;
using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace AspireApp2.Api.Presentation.Controllers;

/// <summary>
/// Controller for managing players
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[Tags("Players")]
public class PlayersController : ControllerBase
{
    private readonly ILogger<PlayersController> _logger;
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;

    public PlayersController(IMediator mediator, IMapper mapper, ILogger<PlayersController> logger)
    {
        _mediator = mediator;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// Get all players or players by team
    /// </summary>
    /// <param name="team">Optional team filter</param>
    /// <param name="cancellationToken"></param>
    /// <returns>List of players</returns>
    /// <response code="200">Returns the list of players</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<PlayerDto>), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<IEnumerable<PlayerDto>>> GetPlayers(
        [FromQuery] string? team = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetPlayersQuery(team);
            IEnumerable<Player> players = await _mediator.Send(query, cancellationToken);

            IEnumerable<PlayerDto> playerDtos = _mapper.Map<IEnumerable<PlayerDto>>(players);

            _logger.LogInformation("Retrieved {Count} players for team: {Team}",
                playerDtos.Count(), team ?? "All teams");

            return Ok(playerDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving players for team: {Team}", team);
            return StatusCode(500, "An error occurred while retrieving players");
        }
    }

    /// <summary>
    /// Get a player by ID
    /// </summary>
    /// <param name="id">Player ID</param>
    /// <param name="cancellationToken"></param>
    /// <returns>Player details</returns>
    /// <response code="200">Returns the player</response>
    /// <response code="404">If the player is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(PlayerDto), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<PlayerDto>> GetPlayer(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetPlayerByIdQuery(id);
            Player? player = await _mediator.Send(query, cancellationToken);

            if (player == null)
            {
                _logger.LogWarning("Player {PlayerId} not found", id);
                return NotFound($"Player with ID {id} not found");
            }

            PlayerDto playerDto = _mapper.Map<PlayerDto>(player);

            _logger.LogInformation("Retrieved player {PlayerId} - {PlayerName}", player.Id, player.FullName);

            return Ok(playerDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving player {PlayerId}", id);
            return StatusCode(500, "An error occurred while retrieving the player");
        }
    }

    /// <summary>
    /// Create a new player
    /// </summary>
    /// <param name="command">Player creation data</param>
    /// <param name="cancellationToken"></param>
    /// <returns>Created player</returns>
    /// <response code="201">Returns the newly created player</response>
    /// <response code="400">If the player data is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPost]
    [ProducesResponseType(typeof(CreatePlayerResponse), (int)HttpStatusCode.Created)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<CreatePlayerResponse>> CreatePlayer(
        [FromBody] CreatePlayerCommand command,
        CancellationToken cancellationToken = default)
    {
        try
        {
            Player player = await _mediator.Send(command, cancellationToken);
            CreatePlayerResponse response = _mapper.Map<CreatePlayerResponse>(player);

            _logger.LogInformation("Created player {PlayerId} - {PlayerName}",
                player.Id, player.FullName);

            return CreatedAtAction(
                nameof(GetPlayer),
                new { id = player.Id },
                response);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning("Business rule violation when creating player: {Error}", ex.Message);
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating player: {FirstName} {LastName}",
                command.FirstName, command.LastName);
            return StatusCode(500, "An error occurred while creating the player");
        }
    }
}
